[General]
log_level = INFO
state_file = smoke_state.npz
default_sunrise = 06:00
default_sunset = 19:00
sun_times_csv = sun_date.csv
watchdog_interval = 30
watchdog_restart_timeout = 60

[LeftCamera]
video_path = C:\stanley\HL_CCTV\HL_CCTV\output.mp4
dataset_dir = ./left_dataset
cache_dir = ./left_cache
roi_config_path = ./left_config.json
image_save_dir = smoke_image_left

[RightCamera]
video_path = C:\stanley\HL_CCTV\HL_CCTV\1747-3.mp4
dataset_dir = ./right_dataset
cache_dir = ./right_cache
roi_config_path = ./right_config.json
image_save_dir = smoke_image_right

[Detection]
process_interval = 1.0
save_interval_high_prob = 60.0
fixed_image_width = 128
fixed_image_height = 128
smoke_threshold = 0.5
min_event_duration = 5
high_prob_threshold_for_daily_count = 0.95
daily_count_interval_minutes = 5

[Classifier]
svm_kernel = linear
svm_probability = True

[Features]
use_hog = True
hog_params = 16,16,2,2
use_lbp = True
lbp_params = 8,1,uniform
use_sift = False

[EmailSettings]
send_email_enabled = False
test_email = False
api_url = http://192.168.14.14/Datarelay/SendMailByJsonHL
mail_to = 0505462
mail_cc = 0803284,1706104
subject_template = HL-CCTV {side} 煙霧偵測警報 ({time})
body_template = 偵測到{side}攝影機區域發生煙霧。\n事件開始時間:{time}\n持續時間:{duration:.1f}秒\n平均嚴重度:{severity:.2f}\n請立即查看影像確認狀況。
system_name = HL-CCTV
user_id = 0803284
min_email_interval = 600

